import json
import os
import re
from datetime import datetime, timedelta
from pathlib import Path

if not os.getenv("COURSE_DUE_TIMES_JSON"):
    try:
        from dotenv import load_dotenv

        load_dotenv()
    except Exception:
        pass

COURSE_DUE_TIMES = json.loads(os.environ.get("COURSE_DUE_TIMES_JSON", "{}"))  # {"Course Substring":"HH:MM", ...}
RESOURCES_DIR = Path(__file__).parent.parent / "resources"
RESOURCES_DIR.mkdir(exist_ok=True)

SCHO_BASE = "https://api.schoology.com/v1"
SCHO_CONSUMER_KEY = os.getenv("SCHOOLOGY_KEY", "")
SCHO_CONSUMER_SECRET = os.getenv("SCHOOLOGY_SECRET", "")
SCHO_USER_UID = os.getenv("SCHOOLOGY_UID", "")  # string ok

CACHE_FILE = RESOURCES_DIR / "schoology_cache.json"
CACHE_MAX_AGE_SECS = 15 * 60
SUBMISSION_CACHE_MAX_AGE_SECS = 10 * 60  # 10 minutes (shorter for submissions)

CURRENT_TZ = datetime.now().astimezone().tzinfo  # local machine tz (e.g., PDT)
EVENT_LENGTH = timedelta(minutes=50)
STACK_EVENTS = os.getenv("STACK_EVENTS", "1") == "1"

CERT_PATH = os.getenv("CERT_PATH", str(RESOURCES_DIR / "certificates/127.0.0.1.pem"))
KEY_PATH = os.getenv("KEY_PATH", str(RESOURCES_DIR / "certificates/127.0.0.1-key.pem"))
DEBUG = os.getenv("DEBUG", "0") == "1"
HOST = os.getenv("HOST", "127.0.0.1")
PORT = int(os.getenv("PORT", "4588"))

# ------------------ MARK AS DONE --------------

# Base URL for mark as done links (will be the server's own URL)
MARK_DONE_BASE_URL = f"https://{HOST}:{PORT}"

# ------------------ REGEX ---------------------
# Named groups so the handler is robust.
RE_ASSIGN_OR_EVENT = re.compile(
    r'(?P<scheme>https?)://[^/]*\.schoology\.com/(?P<type>assignment|event)/(?P<id>\d+)(?:[/?#]|$)',
    re.IGNORECASE
)

# Example: http://bins.schoology.com/course/7916825598/materials/discussion/view/7927769656
RE_DISCUSSION = re.compile(
    r'(?P<scheme>https?)://[^/]*\.schoology\.com/course/\d+/materials/discussion/(?:view/)?(?P<id>\d+)(?:[/?#]|$)',
    re.IGNORECASE
)

RE_LINK_ASSIGN_OR_EVENT = re.compile(f' - Link: {RE_ASSIGN_OR_EVENT.pattern}', re.IGNORECASE)
RE_LINK_DISCUSSION = re.compile(f' - Link: {RE_DISCUSSION.pattern}', re.IGNORECASE)
