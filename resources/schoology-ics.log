Wed Aug 20 08:23:58 PDT 2025 launched
Building Schoology data...
Loaded Schoology data from fresh cache.
Local timezone: PDT
 * Serving Flask app 'main'
 * Debug mode: off
WARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.
 * Running on https://127.0.0.1:4588
Press CTRL+C to quit
127.0.0.1 - - [20/Aug/2025 08:24:15] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [20/Aug/2025 08:39:19] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
127.0.0.1 - - [20/Aug/2025 08:40:52] "GET /mark-done/7927821117 HTTP/1.1" 200 -
127.0.0.1 - - [20/Aug/2025 08:41:12] "GET /mark-done/7927821148 HTTP/1.1" 200 -
127.0.0.1 - - [20/Aug/2025 08:41:15] "GET /mark-done/7927821168 HTTP/1.1" 200 -
127.0.0.1 - - [20/Aug/2025 08:42:10] "GET /?url=https://bins.schoology.com/calendar/feed/ical/1647363511/974e1d0660995b2a9c04c349a1d671fd/ical.ics HTTP/1.1" 200 -
Item 3067015697 not found in cache, will retry after refresh
Item 4659331369 not found in cache, will retry after refresh
Found 2 items missing from cache, refreshing...
Forcing cache refresh...
Force refreshing cache due to missing items...
Preserved 27 existing assignment submissions
Wrote Schoology cache: /Users/<USER>/PycharmProjects/schoology-ics/resources/schoology_cache.json
Item 3067015697 still not found after cache refresh
Item 4659331369 still not found after cache refresh
Item 3067015697 not found in cache, will retry after refresh
Item 4659331369 not found in cache, will retry after refresh
Found 2 items missing from cache, refreshing...
Forcing cache refresh...
Force refreshing cache due to missing items...
Events fetch failed for section 7916821030: ('Connection aborted.', ConnectionResetError(54, 'Connection reset by peer'))
Preserved 27 existing assignment submissions
Wrote Schoology cache: /Users/<USER>/PycharmProjects/schoology-ics/resources/schoology_cache.json
Item 3067015697 still not found after cache refresh
Item 4659331369 still not found after cache refresh
Marked item 7927821117 as done
Marked item 7927821148 as done
Marked item 7927821168 as done
Item 3067015697 not found in cache, will retry after refresh
Item 4659331369 not found in cache, will retry after refresh
Item 7927821192 not found in cache, will retry after refresh
Found 3 items missing from cache, refreshing...
Forcing cache refresh...
Force refreshing cache due to missing items...
Preserved 27 existing assignment submissions
Wrote Schoology cache: /Users/<USER>/PycharmProjects/schoology-ics/resources/schoology_cache.json
Item 3067015697 still not found after cache refresh
Item 4659331369 still not found after cache refresh
Item 7927821192 still not found after cache refresh
Received signal 15, saving cache...
Saved assignment cache on exit: 27 items
Saved assignment cache on exit: 27 items
